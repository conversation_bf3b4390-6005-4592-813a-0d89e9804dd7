#!/usr/bin/env python3

import os
import sys
import subprocess

def main():
    print("Testing new window functionality...")
    
    # Check if we should start in a new window (only if no arguments provided and on Windows)
    if len(sys.argv) == 1 and os.name == 'nt':  # No arguments and on Windows
        # Start in new cmd window and exit current process
        script_path = os.path.abspath(__file__)
        python_exe = sys.executable
        current_dir = os.getcwd()
        
        # Create command to run the script with a special flag in a new window
        cmd = f'start "Test Window" cmd /k "cd /d "{current_dir}" && "{python_exe}" "{script_path}" --in-new-window"'
        
        print("Starting test script in a new command window...")
        subprocess.run(cmd, shell=True)
        return
    
    print("Running in new window mode!")
    print("Current arguments:", sys.argv)
    print("Current directory:", os.getcwd())
    
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
