#!/usr/bin/env python3

import os
import subprocess
import time

# Global variable to hold the persistent cmd process
persistent_cmd_process = None

def initialize_persistent_cmd():
    """Initialize a persistent cmd process for command execution"""
    global persistent_cmd_process
    
    if os.name == 'nt' and persistent_cmd_process is None:  # Windows only
        try:
            # Start a new cmd window that stays open
            persistent_cmd_process = subprocess.Popen(
                'cmd',
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            print("✓ Opened persistent command window for testing")
            return True
        except Exception as e:
            print(f"❌ Failed to create persistent cmd window: {e}")
            return False
    return persistent_cmd_process is not None

def execute_in_persistent_cmd(command):
    """Execute a command in the persistent cmd window"""
    global persistent_cmd_process
    
    if persistent_cmd_process and persistent_cmd_process.poll() is None:
        print(f"Executing: {command}")
        
        # Execute the command with echo for better visibility
        full_command = f'echo [TEST] Executing: {command} && {command} && echo [TEST] Command completed.\n'
        persistent_cmd_process.stdin.write(full_command)
        persistent_cmd_process.stdin.flush()
        
        # Read output with timeout
        start_time = time.time()
        output_lines = []
        timeout = 10
        
        while time.time() - start_time < timeout:
            if persistent_cmd_process.stdout.readable():
                try:
                    line = persistent_cmd_process.stdout.readline()
                    if line:
                        output_lines.append(line.rstrip())
                        print(f"Output: {line.rstrip()}")
                        # Check if command completed
                        if "[TEST] Command completed." in line:
                            break
                    else:
                        time.sleep(0.1)
                except:
                    break
            else:
                time.sleep(0.1)
        
        return "\n".join(output_lines)
    else:
        print("❌ Persistent cmd process not available")
        return None

def cleanup_persistent_cmd():
    """Clean up the persistent cmd process"""
    global persistent_cmd_process
    if persistent_cmd_process and persistent_cmd_process.poll() is None:
        try:
            persistent_cmd_process.stdin.write('exit\n')
            persistent_cmd_process.stdin.flush()
            persistent_cmd_process.wait(timeout=2)
        except:
            persistent_cmd_process.terminate()
        persistent_cmd_process = None
        print("✓ Cleaned up persistent cmd process")

def main():
    print("Testing persistent cmd functionality...")
    
    if not initialize_persistent_cmd():
        print("Failed to initialize persistent cmd")
        return
    
    # Test some commands
    test_commands = [
        "echo Hello from persistent cmd!",
        "dir",
        "echo Current directory: %CD%",
        "python --version"
    ]
    
    for cmd in test_commands:
        print(f"\n--- Testing command: {cmd} ---")
        result = execute_in_persistent_cmd(cmd)
        time.sleep(1)  # Small delay between commands
    
    print("\nTest completed. The cmd window should remain open.")
    input("Press Enter to cleanup and exit...")
    cleanup_persistent_cmd()

if __name__ == "__main__":
    main()
