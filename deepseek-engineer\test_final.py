#!/usr/bin/env python3

import os
import subprocess
import time

def test_api_key():
    """Test API key configuration"""
    from dotenv import load_dotenv
    load_dotenv()
    
    # Get API key from environment or use default
    api_key = os.getenv("DEEPSEEK_API_KEY", "***********************************")
    
    print(f"✓ API Key loaded: {api_key[:10]}...{api_key[-10:]}")
    print(f"✓ API Key length: {len(api_key)}")
    
    if api_key.startswith("sk-"):
        print("✓ API key format looks correct")
        return True
    else:
        print("✗ API key format looks incorrect")
        return False

def test_persistent_cmd():
    """Test persistent cmd functionality"""
    print("\n--- Testing Persistent CMD Functionality ---")
    
    if os.name != 'nt':
        print("⚠ Persistent cmd functionality is Windows-only")
        return True
    
    try:
        # Start a new cmd window that stays open
        process = subprocess.Popen(
            'cmd',
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            creationflags=subprocess.CREATE_NEW_CONSOLE
        )
        print("✓ Successfully created persistent cmd window")
        
        # Test a simple command
        test_command = 'echo [TEST] Hello from persistent cmd && echo [TEST] Command completed.\n'
        process.stdin.write(test_command)
        process.stdin.flush()
        
        # Read some output
        start_time = time.time()
        while time.time() - start_time < 3:
            if process.stdout.readable():
                try:
                    line = process.stdout.readline()
                    if line and "[TEST] Command completed." in line:
                        print("✓ Command execution and output reading works")
                        break
                except:
                    break
            time.sleep(0.1)
        
        # Cleanup
        try:
            process.stdin.write('exit\n')
            process.stdin.flush()
            process.wait(timeout=2)
        except:
            process.terminate()
        
        print("✓ Cleanup successful")
        return True
        
    except Exception as e:
        print(f"✗ Error testing persistent cmd: {e}")
        return False

def test_imports():
    """Test that all required modules can be imported"""
    print("\n--- Testing Module Imports ---")
    
    try:
        import os
        print("✓ os")
        
        import sys
        print("✓ sys")
        
        import json
        print("✓ json")
        
        import subprocess
        print("✓ subprocess")
        
        from pathlib import Path
        print("✓ pathlib.Path")
        
        from textwrap import dedent
        print("✓ textwrap.dedent")
        
        from typing import List, Dict, Any, Optional
        print("✓ typing")
        
        try:
            from openai import OpenAI
            print("✓ openai.OpenAI")
        except ImportError:
            print("⚠ openai not available (will need uv run)")
        
        try:
            from pydantic import BaseModel
            print("✓ pydantic.BaseModel")
        except ImportError:
            print("⚠ pydantic not available (will need uv run)")
        
        try:
            from dotenv import load_dotenv
            print("✓ dotenv.load_dotenv")
        except ImportError:
            print("⚠ python-dotenv not available (will need uv run)")
        
        try:
            from rich.console import Console
            from rich.table import Table
            from rich.panel import Panel
            print("✓ rich components")
        except ImportError:
            print("⚠ rich not available (will need uv run)")
        
        try:
            from prompt_toolkit import PromptSession
            from prompt_toolkit.styles import Style as PromptStyle
            print("✓ prompt_toolkit")
        except ImportError:
            print("⚠ prompt_toolkit not available (will need uv run)")
        
        import time
        print("✓ time")
        
        return True
        
    except Exception as e:
        print(f"✗ Import error: {e}")
        return False

def main():
    print("🧪 DeepSeek Engineer - Final Functionality Test")
    print("=" * 50)
    
    results = []
    
    # Test API key
    print("\n--- Testing API Key Configuration ---")
    results.append(test_api_key())
    
    # Test imports
    results.append(test_imports())
    
    # Test persistent cmd
    results.append(test_persistent_cmd())
    
    # Summary
    print("\n" + "=" * 50)
    print("🏁 Test Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ All tests passed ({passed}/{total})")
        print("\n🎉 DeepSeek Engineer is ready to use!")
        print("💡 Run: uv run deepseek-eng.py")
    else:
        print(f"⚠ Some tests failed ({passed}/{total})")
        print("💡 You may need to install dependencies with: uv sync")

if __name__ == "__main__":
    main()
