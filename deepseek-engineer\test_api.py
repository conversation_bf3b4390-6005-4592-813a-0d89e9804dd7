#!/usr/bin/env python3

import os
from dotenv import load_dotenv

def test_api_key():
    load_dotenv()
    
    # Get API key from environment or use default
    api_key = os.getenv("DEEPSEEK_API_KEY", "***********************************")
    
    print(f"API Key loaded: {api_key[:10]}...{api_key[-10:]}")
    print(f"API Key length: {len(api_key)}")
    
    if api_key.startswith("sk-"):
        print("✓ API key format looks correct")
    else:
        print("✗ API key format looks incorrect")

if __name__ == "__main__":
    test_api_key()
