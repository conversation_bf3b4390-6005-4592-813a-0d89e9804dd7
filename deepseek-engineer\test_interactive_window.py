#!/usr/bin/env python3

import os
import subprocess

def test_interactive_window():
    """Test creating an interactive cmd window"""
    current_dir = os.getcwd()
    print(f"Current directory: {current_dir}")
    
    try:
        # Create an interactive cmd window
        subprocess.<PERSON>n(
            f'start "Test Interactive CMD" cmd /k "cd /d "{current_dir}" && echo. && echo ======================================== && echo   Test Interactive CMD Window && echo ======================================== && echo   Directory: {current_dir} && echo   You can type commands here && echo ======================================== && echo."',
            shell=True,
            cwd=current_dir
        )
        print("✓ Opened interactive cmd window")
        
        # Test sending a command to it
        subprocess.<PERSON>n(
            f'start /min cmd /c "echo [TEST] This is a test command && dir && echo [TEST] Command completed. && timeout /t 3 >nul"',
            shell=True,
            cwd=current_dir
        )
        print("✓ Sent test command to window")
        
        print("\nCheck your screen:")
        print("1. You should see an interactive cmd window titled 'Test Interactive CMD'")
        print("2. You should be able to type commands in that window")
        print("3. You should see a test command execution")
        
        input("Press Enter to exit...")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_interactive_window()
