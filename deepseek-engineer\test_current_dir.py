#!/usr/bin/env python3

import os
import sys
import subprocess
from rich.console import Console

console = Console()

# Global variable to hold the persistent cmd process
persistent_cmd_process = None

def initialize_persistent_cmd():
    """Initialize a persistent cmd process for command execution"""
    global persistent_cmd_process
    
    if os.name == 'nt' and persistent_cmd_process is None:  # Windows only
        try:
            console.print("[yellow]🔄[/yellow] Creating persistent cmd window...")
            # Get current working directory
            current_dir = os.getcwd()
            console.print(f"[dim]Opening cmd in directory: {current_dir}[/dim]")
            
            # Start a new cmd window that stays open in current directory
            persistent_cmd_process = subprocess.Popen(
                'cmd',
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                cwd=current_dir,  # 设置工作目录为当前目录
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            console.print("[bold green]✓[/bold green] Opened persistent command window for AI operations")
            return True
        except Exception as e:
            console.print(f"[bold red]❌[/bold red] Failed to create persistent cmd window: {e}")
            return False
    elif os.name != 'nt':
        console.print("[yellow]⚠[/yellow] Persistent cmd window is Windows-only feature")
    return persistent_cmd_process is not None

def test_current_directory():
    """Test that cmd opens in current directory"""
    global persistent_cmd_process
    
    if persistent_cmd_process and persistent_cmd_process.poll() is None:
        console.print("[bold yellow]⚡[/bold yellow] Testing current directory...")
        
        # Test current directory
        test_command = 'echo Current directory: %CD%\n'
        persistent_cmd_process.stdin.write(test_command)
        persistent_cmd_process.stdin.flush()
        
        console.print("[bold green]✓[/bold green] Command sent to check current directory")
        
        # Test listing files
        test_command2 = 'dir /b\n'
        persistent_cmd_process.stdin.write(test_command2)
        persistent_cmd_process.stdin.flush()
        
        console.print("[bold green]✓[/bold green] Command sent to list files")
        
        return True
    else:
        console.print("[bold red]❌[/bold red] Persistent cmd process not available")
        return False

def main():
    console.print("🧪 Testing Current Directory Setup")
    console.print("=" * 40)
    
    console.print(f"[bold blue]Python script running in:[/bold blue] {os.getcwd()}")
    
    # Initialize
    if initialize_persistent_cmd():
        console.print("\n✅ Initialization successful")
        
        # Test current directory
        if test_current_directory():
            console.print("\n✅ Directory test commands sent")
            console.print("\n[bold cyan]Check the cmd window to see:[/bold cyan]")
            console.print("1. Current directory should match Python script directory")
            console.print("2. File listing should show project files")
        
        input("\nPress Enter to exit...")
    else:
        console.print("\n❌ Initialization failed")

if __name__ == "__main__":
    main()
