#!/usr/bin/env python3

import os
import subprocess

def test_cmd_windows():
    """Test opening cmd windows"""
    current_dir = os.getcwd()
    print(f"Current directory: {current_dir}")
    
    # Test opening interactive cmd window
    try:
        subprocess.Popen(
            f'start "Test Interactive CMD" cmd /k "cd /d "{current_dir}" && echo. && echo ======================================== && echo   Test Interactive CMD Window && echo ======================================== && echo   Directory: {current_dir} && echo   You can type commands here && echo ======================================== && echo."',
            shell=True,
            cwd=current_dir
        )
        print("✓ Opened interactive cmd window")
        
        # Test opening AI cmd window
        ai_process = subprocess.Popen(
            'cmd',
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            cwd=current_dir,
            creationflags=subprocess.CREATE_NEW_CONSOLE
        )
        print("✓ Created AI cmd process")
        
        # Test sending a command to AI process
        ai_process.stdin.write('echo Hello from AI process\n')
        ai_process.stdin.flush()
        print("✓ Sent test command to AI process")
        
        print("\nCheck your screen for two cmd windows:")
        print("1. Interactive CMD - you can type commands")
        print("2. AI CMD - for AI command execution")
        
        input("Press Enter to exit...")
        
        # Cleanup
        try:
            ai_process.stdin.write('exit\n')
            ai_process.stdin.flush()
            ai_process.wait(timeout=2)
        except:
            ai_process.terminate()
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_cmd_windows()
