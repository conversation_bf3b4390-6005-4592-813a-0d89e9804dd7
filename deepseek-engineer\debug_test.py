#!/usr/bin/env python3

import os
import sys
import subprocess
from rich.console import Console

console = Console()

# Global variable to hold the persistent cmd process
persistent_cmd_process = None

def initialize_persistent_cmd():
    """Initialize a persistent cmd process for command execution"""
    global persistent_cmd_process
    
    console.print(f"[dim]Debug: OS={os.name}, existing process={persistent_cmd_process is not None}[/dim]")
    
    if os.name == 'nt' and persistent_cmd_process is None:  # Windows only
        try:
            console.print("[yellow]🔄[/yellow] Creating persistent cmd window...")
            # Start a new cmd window that stays open
            persistent_cmd_process = subprocess.Popen(
                'cmd',
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            console.print("[bold green]✓[/bold green] Opened persistent command window for AI operations")
            console.print(f"[dim]Debug: Process PID={persistent_cmd_process.pid}[/dim]")
            return True
        except Exception as e:
            console.print(f"[bold red]❌[/bold red] Failed to create persistent cmd window: {e}")
            return False
    elif os.name != 'nt':
        console.print("[yellow]⚠[/yellow] Persistent cmd window is Windows-only feature")
    return persistent_cmd_process is not None

def test_execute_command(command: str):
    """Test command execution"""
    global persistent_cmd_process
    
    console.print(f"[bold yellow]⚡[/bold yellow] Testing command: [bright_cyan]{command}[/bright_cyan]")
    
    # Debug information
    console.print(f"[dim]Debug: persistent_cmd_process={persistent_cmd_process is not None}[/dim]")
    if persistent_cmd_process:
        console.print(f"[dim]Debug: process poll={persistent_cmd_process.poll()}[/dim]")

    # Use persistent cmd process if available
    if persistent_cmd_process and persistent_cmd_process.poll() is None:
        console.print(f"[bold cyan]📺[/bold cyan] Executing in persistent cmd window...")
        
        # Execute the command with echo for better visibility
        full_command = f'echo [TEST] Executing: {command} && {command} && echo [TEST] Command completed.\n'
        persistent_cmd_process.stdin.write(full_command)
        persistent_cmd_process.stdin.flush()
        
        console.print("[bold green]✓[/bold green] Command sent to persistent window")
        return "Command sent to persistent cmd window"
    else:
        console.print("[bold red]❌[/bold red] Persistent cmd process not available")
        return "Persistent cmd process not available"

def main():
    console.print("🧪 Testing Persistent CMD Debug")
    console.print("=" * 40)
    
    # Initialize
    if initialize_persistent_cmd():
        console.print("\n✅ Initialization successful")
        
        # Test some commands
        test_commands = [
            "echo Hello World!",
            "dir",
            "python --version"
        ]
        
        for cmd in test_commands:
            console.print(f"\n--- Testing: {cmd} ---")
            result = test_execute_command(cmd)
            console.print(f"Result: {result}")
            
            input("Press Enter to continue...")
    else:
        console.print("\n❌ Initialization failed")
    
    console.print("\nTest completed.")

if __name__ == "__main__":
    main()
