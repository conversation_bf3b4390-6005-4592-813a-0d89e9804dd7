#!/usr/bin/env python3

import os
import sys
import subprocess
import time
from rich.console import Console

console = Console()

# Global variable to hold the persistent cmd process
persistent_cmd_process = None

def initialize_persistent_cmd():
    """Initialize a persistent cmd process for command execution"""
    global persistent_cmd_process
    
    if os.name == 'nt' and persistent_cmd_process is None:  # Windows only
        try:
            console.print("[yellow]🔄[/yellow] Creating persistent cmd window...")
            # Start a new cmd window that stays open
            persistent_cmd_process = subprocess.Popen(
                'cmd',
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            console.print("[bold green]✓[/bold green] Opened persistent command window for AI operations")
            return True
        except Exception as e:
            console.print(f"[bold red]❌[/bold red] Failed to create persistent cmd window: {e}")
            return False
    elif os.name != 'nt':
        console.print("[yellow]⚠[/yellow] Persistent cmd window is Windows-only feature")
    return persistent_cmd_process is not None

def execute_command(command: str, working_directory: str = None, timeout: int = 30, show_window: bool = True) -> str:
    """Execute a command line instruction and return the output."""
    global persistent_cmd_process
    
    try:
        console.print(f"[bold yellow]⚡[/bold yellow] Executing command: [bright_cyan]{command}[/bright_cyan]")
        if working_directory:
            console.print(f"[dim]Working directory: {working_directory}[/dim]")

        # Check persistent cmd process status
        if persistent_cmd_process and persistent_cmd_process.poll() is not None:
            console.print("[yellow]⚠[/yellow] Persistent cmd process has terminated, reinitializing...")
            initialize_persistent_cmd()

        # Use persistent cmd process if available and show_window is True
        if show_window and persistent_cmd_process and persistent_cmd_process.poll() is None:
            console.print(f"[bold cyan]📺[/bold cyan] Executing in persistent cmd window...")
            
            # Change directory if needed
            if working_directory:
                cd_command = f'cd /d "{working_directory}"\n'
                persistent_cmd_process.stdin.write(cd_command)
                persistent_cmd_process.stdin.flush()

            # Execute the command with echo for better visibility
            full_command = f'echo [AI] Executing: {command} && {command} && echo [AI] Command completed.\n'
            persistent_cmd_process.stdin.write(full_command)
            persistent_cmd_process.stdin.flush()

            # Read output with timeout
            start_time = time.time()
            output_lines = []
            command_started = False

            while time.time() - start_time < timeout:
                if persistent_cmd_process.stdout.readable():
                    try:
                        line = persistent_cmd_process.stdout.readline()
                        if line:
                            line_clean = line.rstrip()

                            # Start capturing after we see the execution message
                            if "[AI] Executing:" in line_clean:
                                command_started = True
                                output_lines.append(line_clean)
                            elif command_started:
                                output_lines.append(line_clean)
                                # Check if command completed
                                if "[AI] Command completed." in line_clean:
                                    break
                        else:
                            time.sleep(0.1)
                    except:
                        break
                else:
                    time.sleep(0.1)

            # Filter out the echo messages for cleaner output
            filtered_output = []
            for line in output_lines:
                if not line.startswith("[AI] Executing:") and not line.startswith("[AI] Command completed."):
                    filtered_output.append(line)

            output = "\n".join(filtered_output) if filtered_output else "(No output captured)"
            console.print(f"[bold green]✓[/bold green] Command executed in persistent window")

            return f"Command: {command}\nExecuted in persistent cmd window\n\n{output}"

        else:
            console.print("[bold red]❌[/bold red] Falling back to standard execution")
            # Fall back to standard execution
            result = subprocess.run(
                command,
                shell=True,
                cwd=working_directory or os.getcwd(),
                capture_output=True,
                text=True,
                timeout=timeout
            )

            # Format the output
            output_parts = []
            if result.stdout:
                output_parts.append(f"STDOUT:\n{result.stdout}")
            if result.stderr:
                output_parts.append(f"STDERR:\n{result.stderr}")

            output = "\n\n".join(output_parts) if output_parts else "(No output)"

            # Add return code information
            status_msg = f"Command completed with return code: {result.returncode}"
            if result.returncode == 0:
                console.print(f"[bold green]✓[/bold green] {status_msg}")
            else:
                console.print(f"[bold red]✗[/bold red] {status_msg}")

            return f"Command: {command}\nReturn code: {result.returncode}\n\n{output}"

    except subprocess.TimeoutExpired:
        error_msg = f"Command timed out after {timeout} seconds"
        console.print(f"[bold red]⏰[/bold red] {error_msg}")
        return f"Command: {command}\nError: {error_msg}"
    except Exception as e:
        error_msg = f"Error executing command: {str(e)}"
        console.print(f"[bold red]❌[/bold red] {error_msg}")
        return f"Command: {command}\nError: {error_msg}"

def main():
    console.print("🧪 Testing execute_command function")
    console.print("=" * 40)
    
    # Initialize
    if initialize_persistent_cmd():
        console.print("\n✅ Initialization successful")
        
        # Test some commands
        test_commands = [
            "echo Hello from persistent cmd!",
            "python --version",
            "dir"
        ]
        
        for cmd in test_commands:
            console.print(f"\n--- Testing: {cmd} ---")
            result = execute_command(cmd)
            console.print(f"[bold blue]Result:[/bold blue]\n{result}")
            
            input("\nPress Enter to continue...")
    else:
        console.print("\n❌ Initialization failed")
    
    console.print("\nTest completed.")

if __name__ == "__main__":
    main()
